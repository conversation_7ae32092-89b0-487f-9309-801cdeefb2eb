import React from "react";
import { motion } from "framer-motion";
import {
  BarChart3,
  <PERSON><PERSON><PERSON>,
  HelpCircle,
  RotateCcw,
  Trophy,
  Zap,
} from "lucide-react";
import { useGameStore } from "@/stores/gameStore";

export function Header() {
  const {
    toggleStats,
    toggleSettings,
    toggleHelp,
    resetGame,
    stats,
    settings,
    gameState,
  } = useGameStore();

  const headerButtons = [
    {
      icon: HelpCircle,
      onClick: toggleHelp,
      label: "Help",
      color: "text-blue-500 hover:text-blue-600",
    },
    {
      icon: BarChart3,
      onClick: toggleStats,
      label: "Statistics",
      color: "text-green-500 hover:text-green-600",
      badge: stats.currentStreak > 0 ? stats.currentStreak : undefined,
    },
    {
      icon: Settings,
      onClick: toggleSettings,
      label: "Settings",
      color: "text-gray-500 hover:text-gray-600",
    },
    {
      icon: RotateCcw,
      onClick: resetGame,
      label: "New Game",
      color: "text-purple-500 hover:text-purple-600",
    },
  ];

  return (
    <motion.header
      className="flex items-center justify-between py-4 border-b border-gray-200 dark:border-gray-700"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center space-x-2">
        <motion.div
          className="flex items-center space-x-2"
          whileHover={{ scale: 1.05 }}
        >
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">W</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              Wordly2
            </h1>
            <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
              <span className="capitalize">{settings.difficulty}</span>
              {settings.difficulty === "hard" && (
                <Zap size={12} className="text-warning-500" />
              )}
              {settings.difficulty === "expert" && (
                <Trophy size={12} className="text-error-500" />
              )}
            </div>
          </div>
        </motion.div>
      </div>

      <div className="flex items-center space-x-2">
        {headerButtons.map(
          ({ icon: Icon, onClick, label, color, badge }, index) => (
            <motion.button
              key={label}
              onClick={onClick}
              className={`relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors ${color}`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.3,
                delay: index * 0.1,
                type: "spring",
                stiffness: 300,
                damping: 30,
              }}
              title={label}
            >
              <Icon size={20} />
              {badge && (
                <motion.div
                  className="absolute -top-1 -right-1 bg-success-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  {badge}
                </motion.div>
              )}
            </motion.button>
          )
        )}
      </div>
    </motion.header>
  );
}

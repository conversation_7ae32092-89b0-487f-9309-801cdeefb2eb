import React from "react";
import { motion } from "framer-motion";
import { Trophy, Target, Zap, TrendingUp, Calendar, Award } from "lucide-react";
import { useGameStore } from "@/stores/gameStore";
import { Modal } from "./Modal";

export function StatsModal() {
  const { showStats, toggleStats, stats } = useGameStore();

  const winPercentage =
    stats.gamesPlayed > 0
      ? Math.round((stats.gamesWon / stats.gamesPlayed) * 100)
      : 0;

  const statItems = [
    {
      icon: Trophy,
      label: "Games Won",
      value: stats.gamesWon,
      color: "text-success-500",
    },
    {
      icon: Target,
      label: "Win Rate",
      value: `${winPercentage}%`,
      color: "text-primary",
    },
    {
      icon: Zap,
      label: "Current Streak",
      value: stats.currentStreak,
      color: "text-warning-500",
    },
    {
      icon: TrendingUp,
      label: "Max Streak",
      value: stats.maxStreak,
      color: "text-error-500",
    },
    {
      icon: Calendar,
      label: "Games Played",
      value: stats.gamesPlayed,
      color: "text-gray-500 dark:text-gray-400",
    },
    {
      icon: Award,
      label: "Avg Guesses",
      value: stats.averageGuesses > 0 ? stats.averageGuesses.toFixed(1) : "0",
      color: "text-purple-500",
    },
  ];

  const maxGuesses = 6;
  const maxCount = Math.max(...Object.values(stats.guessDistribution), 1);

  return (
    <Modal
      isOpen={showStats}
      onClose={toggleStats}
      title="Statistics"
      maxWidth="max-w-lg"
    >
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4">
          {statItems.map(({ icon: Icon, label, value, color }, index) => (
            <motion.div
              key={label}
              className="bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-4 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center justify-center mb-2">
                <Icon className={`w-6 h-6 ${color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {value}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {label}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Guess Distribution */}
        {stats.gamesWon > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Target className="w-5 h-5" />
              Guess Distribution
            </h3>
            <div className="space-y-2">
              {Array.from({ length: maxGuesses }, (_, i) => {
                const guessNumber = i + 1;
                const count = stats.guessDistribution[guessNumber] || 0;
                const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;

                return (
                  <motion.div
                    key={guessNumber}
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: guessNumber * 0.1 }}
                  >
                    <span className="w-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {guessNumber}
                    </span>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-6 relative overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-end pr-2"
                        initial={{ width: 0 }}
                        animate={{
                          width: `${Math.max(percentage, count > 0 ? 10 : 0)}%`,
                        }}
                        transition={{
                          delay: guessNumber * 0.1 + 0.3,
                          duration: 0.5,
                        }}
                      >
                        {count > 0 && (
                          <span className="text-xs font-medium text-white">
                            {count}
                          </span>
                        )}
                      </motion.div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        )}

        {/* Additional Stats */}
        {stats.gamesPlayed > 0 && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {Math.round(stats.totalPlayTime / 60)}m
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Total Play Time
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {stats.hintsUsed}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Hints Used
              </div>
            </div>
          </div>
        )}

        {stats.gamesPlayed === 0 && (
          <div className="text-center py-8">
            <Trophy className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              Play your first game to see statistics!
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
}

import React, { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Toaster } from "react-hot-toast";
import { useGameStore } from "@/stores/gameStore";
import { GameBoard } from "@/components/GameBoard";
import { Keyboard } from "@/components/Keyboard";
import { Header } from "@/components/Header";
import { StatsModal } from "@/components/StatsModal";
import { SettingsModal } from "@/components/SettingsModal";
import { HelpModal } from "@/components/HelpModal";
import { GameOverModal } from "@/components/GameOverModal";
import { ErrorToast } from "@/components/ErrorToast";

function App() {
  const {
    gameState,
    error,
    showStats,
    showSettings,
    showHelp,
    settings,
    startNewGame,
    setError,
  } = useGameStore();

  // Initialize game on first load
  useEffect(() => {
    startNewGame(settings.difficulty);
  }, []);

  // Handle keyboard input
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (gameState !== "playing") return;

      const key = event.key.toUpperCase();

      if (key === "ENTER") {
        useGameStore.getState().submitGuess();
      } else if (key === "BACKSPACE") {
        useGameStore.getState().removeLetter();
      } else if (/^[A-Z]$/.test(key)) {
        useGameStore.getState().addLetter(key);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [gameState]);

  // Clear error after 3 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [error, setError]);

  return (
    <div
      className={`min-h-screen transition-colors duration-300 ${
        settings.darkMode ? "dark" : ""
      }`}
    >
      <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        <div className="container mx-auto max-w-lg px-4 py-6">
          <Header />

          <main className="flex flex-col items-center space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <GameBoard />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="w-full"
            >
              <Keyboard />
            </motion.div>
          </main>
        </div>

        {/* Modals */}
        <AnimatePresence>
          {showStats && <StatsModal />}
          {showSettings && <SettingsModal />}
          {showHelp && <HelpModal />}
          {(gameState === "won" || gameState === "lost") && <GameOverModal />}
        </AnimatePresence>

        {/* Error Toast */}
        <ErrorToast />

        {/* React Hot Toast */}
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 2000,
            style: {
              background: settings.darkMode ? "#1f2937" : "#ffffff",
              color: settings.darkMode ? "#f9fafb" : "#111827",
              border: `1px solid ${settings.darkMode ? "#374151" : "#e5e7eb"}`,
            },
          }}
        />
      </div>
    </div>
  );
}

export default App;

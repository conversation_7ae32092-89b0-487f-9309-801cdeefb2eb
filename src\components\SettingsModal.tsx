import React from "react";
import { motion } from "framer-motion";
import {
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Zap,
  Eye,
  Sparkles,
  Lightbulb,
  Shield,
} from "lucide-react";
import { useGameStore } from "@/stores/gameStore";
import { Modal } from "./Modal";
import type { Difficulty } from "@/types";

export function SettingsModal() {
  const { showSettings, toggleSettings, settings, updateSettings } =
    useGameStore();

  const difficultyOptions: {
    value: Difficulty;
    label: string;
    description: string;
    icon: React.ReactNode;
  }[] = [
    {
      value: "easy",
      label: "Easy",
      description: "4 letters, 7 guesses, hints available",
      icon: <Lightbulb className="w-4 h-4 text-green-500" />,
    },
    {
      value: "normal",
      label: "Normal",
      description: "5 letters, 6 guesses, classic mode",
      icon: <Shield className="w-4 h-4 text-blue-500" />,
    },
    {
      value: "hard",
      label: "Hard",
      description: "6 letters, 6 guesses, time limit",
      icon: <Zap className="w-4 h-4 text-orange-500" />,
    },
    {
      value: "expert",
      label: "Expert",
      description: "7 letters, 5 guesses, no hints",
      icon: <Sparkles className="w-4 h-4 text-red-500" />,
    },
  ];

  const toggleOptions = [
    {
      key: "darkMode" as keyof typeof settings,
      label: "Dark Mode",
      description: "Switch between light and dark themes",
      icon: settings.darkMode ? (
        <Moon className="w-4 h-4" />
      ) : (
        <Sun className="w-4 h-4" />
      ),
    },
    {
      key: "sounds" as keyof typeof settings,
      label: "Sound Effects",
      description: "Play sounds for game interactions",
      icon: settings.sounds ? (
        <Volume2 className="w-4 h-4" />
      ) : (
        <VolumeX className="w-4 h-4" />
      ),
    },
    {
      key: "animations" as keyof typeof settings,
      label: "Animations",
      description: "Enable tile flip and bounce animations",
      icon: <Sparkles className="w-4 h-4" />,
    },
    {
      key: "colorBlindMode" as keyof typeof settings,
      label: "Color Blind Mode",
      description: "Use high contrast colors and patterns",
      icon: <Eye className="w-4 h-4" />,
    },
    {
      key: "hintsEnabled" as keyof typeof settings,
      label: "Hints",
      description: "Allow hint usage during games",
      icon: <Lightbulb className="w-4 h-4" />,
    },
    {
      key: "hardMode" as keyof typeof settings,
      label: "Hard Mode",
      description: "Revealed hints must be used in subsequent guesses",
      icon: <Shield className="w-4 h-4" />,
    },
  ];

  const handleDifficultyChange = (difficulty: Difficulty) => {
    updateSettings({ difficulty });
  };

  const handleToggle = (key: keyof typeof settings) => {
    updateSettings({ [key]: !settings[key] });
  };

  return (
    <Modal
      isOpen={showSettings}
      onClose={toggleSettings}
      title="Settings"
      maxWidth="max-w-lg"
    >
      <div className="space-y-6">
        {/* Difficulty Selection */}
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Difficulty
          </h3>
          <div className="grid grid-cols-1 gap-3">
            {difficultyOptions.map(
              ({ value, label, description, icon }, index) => (
                <motion.button
                  key={value}
                  onClick={() => handleDifficultyChange(value)}
                  className={`p-4 rounded-lg border-2 transition-all text-left ${
                    settings.difficulty === value
                      ? "border-primary bg-primary/10"
                      : "border-gray-200 dark:border-gray-700 hover:border-blue-500/50 hover:bg-gray-100/50 dark:hover:bg-gray-800/50"
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    {icon}
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {label}
                    </span>
                    {settings.difficulty === value && (
                      <div className="ml-auto w-2 h-2 bg-primary rounded-full" />
                    )}
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {description}
                  </p>
                </motion.button>
              )
            )}
          </div>
        </div>

        {/* Toggle Options */}
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Preferences
          </h3>
          <div className="space-y-3">
            {toggleOptions.map(({ key, label, description, icon }, index) => (
              <motion.div
                key={key}
                className="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center gap-3">
                  {icon}
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {label}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {description}
                    </div>
                  </div>
                </div>
                <motion.button
                  onClick={() => handleToggle(key)}
                  className={`relative w-12 h-6 rounded-full transition-colors ${
                    settings[key]
                      ? "bg-blue-500"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="absolute top-1 w-4 h-4 bg-white rounded-full shadow-sm"
                    animate={{
                      x: settings[key] ? 26 : 2,
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Reset Warning */}
        <motion.div
          className="bg-warning-500/10 border border-warning-500/20 rounded-lg p-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-sm text-warning-700 dark:text-warning-300">
            <strong>Note:</strong> Changing difficulty will start a new game
            with your current progress lost.
          </p>
        </motion.div>
      </div>
    </Modal>
  );
}

import React from "react";
import { motion } from "framer-motion";
import {
  Target,
  Keyboard,
  Lightbulb,
  Trophy,
  Zap,
  Eye,
  Share2,
} from "lucide-react";
import { useGameStore } from "@/stores/gameStore";
import { Modal } from "./Modal";

export function HelpModal() {
  const { showHelp, toggleHelp } = useGameStore();

  const examples = [
    {
      word: "WEARY",
      letters: [
        { char: "W", state: "absent" },
        { char: "E", state: "present" },
        { char: "A", state: "absent" },
        { char: "R", state: "correct" },
        { char: "Y", state: "absent" },
      ],
      explanation:
        "W, A, Y are not in the word. E is in the word but wrong position. R is correct!",
    },
  ];

  const features = [
    {
      icon: <Target className="w-5 h-5 text-blue-500" />,
      title: "Multiple Difficulties",
      description: "Choose from Easy (4 letters) to Expert (7 letters) modes",
    },
    {
      icon: <Lightbulb className="w-5 h-5 text-yellow-500" />,
      title: "Smart Hints",
      description: "Get helpful hints when you're stuck (if enabled)",
    },
    {
      icon: <Trophy className="w-5 h-5 text-green-500" />,
      title: "Statistics Tracking",
      description: "Track your progress, streaks, and performance over time",
    },
    {
      icon: <Zap className="w-5 h-5 text-red-500" />,
      title: "Animations & Effects",
      description: "Beautiful animations and smooth transitions",
    },
    {
      icon: <Eye className="w-5 h-5 text-purple-500" />,
      title: "Accessibility",
      description: "Color blind mode and keyboard navigation support",
    },
    {
      icon: <Share2 className="w-5 h-5 text-blue-500" />,
      title: "Share Results",
      description: "Share your achievements with friends",
    },
  ];

  return (
    <Modal
      isOpen={showHelp}
      onClose={toggleHelp}
      title="How to Play"
      maxWidth="max-w-2xl"
    >
      <div className="space-y-6">
        {/* Basic Rules */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Target className="w-5 h-5" />
            Objective
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Guess the hidden word in the given number of tries. Each guess must
            be a valid word. After each guess, the color of the tiles will
            change to show how close your guess was.
          </p>
        </div>

        {/* Color Guide */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Color Guide</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center text-white font-bold text-sm">
                R
              </div>
              <span className="text-gray-500 dark:text-gray-400">
                <strong className="text-gray-900 dark:text-gray-100">Green:</strong> Letter is
                correct and in the right position
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-yellow-500 rounded flex items-center justify-center text-white font-bold text-sm">
                E
              </div>
              <span className="text-gray-500 dark:text-gray-400">
                <strong className="text-gray-900 dark:text-gray-100">Yellow:</strong> Letter is
                in the word but wrong position
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-500 rounded flex items-center justify-center text-white font-bold text-sm">
                W
              </div>
              <span className="text-gray-500 dark:text-gray-400">
                <strong className="text-gray-900 dark:text-gray-100">Gray:</strong> Letter is not
                in the word
              </span>
            </div>
          </div>
        </div>

        {/* Example */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Example</h3>
          {examples.map((example, index) => (
            <motion.div
              key={index}
              className="bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex gap-2 mb-3 justify-center">
                {example.letters.map((letter, letterIndex) => (
                  <div
                    key={letterIndex}
                    className={`w-10 h-10 rounded flex items-center justify-center text-white font-bold ${
                      letter.state === "correct"
                        ? "bg-green-500"
                        : letter.state === "present"
                        ? "bg-yellow-500"
                        : "bg-gray-500"
                    }`}
                  >
                    {letter.char}
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                {example.explanation}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Controls */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Keyboard className="w-5 h-5" />
            Controls
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                A-Z
              </kbd>
              <span className="text-gray-500 dark:text-gray-400">Type letters</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                Enter
              </kbd>
              <span className="text-gray-500 dark:text-gray-400">Submit guess</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                Backspace
              </kbd>
              <span className="text-gray-500 dark:text-gray-400">Delete letter</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                Esc
              </kbd>
              <span className="text-gray-500 dark:text-gray-400">Close modals</span>
            </div>
          </div>
        </div>

        {/* Features */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="flex items-start gap-3 p-3 rounded-lg bg-gray-100/30 dark:bg-gray-800/30"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                {feature.icon}
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                    {feature.title}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Tips */}
        <motion.div
          className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-lg font-semibold mb-2 text-blue-600 dark:text-blue-400">
            💡 Pro Tips
          </h3>
          <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
            <li>• Start with words containing common vowels (A, E, I, O, U)</li>
            <li>• Use common consonants like R, S, T, L, N in your first guesses</li>
            <li>• Pay attention to letter frequency in your language</li>
            <li>• In hard mode, you must use revealed letters in subsequent guesses</li>
            <li>• Use hints wisely - they're limited in higher difficulties</li>
          </ul>
        </motion.div>
      </div>
    </Modal>
  );
}

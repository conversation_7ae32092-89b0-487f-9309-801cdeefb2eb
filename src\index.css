@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-sans;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* Focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }

  /* Game-specific styles */
  .letter-tile {
    @apply w-14 h-14 border-2 border-gray-300 dark:border-gray-600 rounded-md flex items-center justify-center text-2xl font-bold uppercase transition-all duration-300;
  }

  .letter-tile.empty {
    @apply bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600;
  }

  .letter-tile.filled {
    @apply bg-gray-100 dark:bg-gray-800 border-gray-400 dark:border-gray-500;
  }

  .letter-tile.correct {
    @apply bg-green-500 border-green-500 text-white;
  }

  .letter-tile.present {
    @apply bg-yellow-500 border-yellow-500 text-white;
  }

  .letter-tile.absent {
    @apply bg-gray-500 dark:bg-gray-600 border-gray-500 dark:border-gray-600 text-white;
  }

  /* Keyboard styles */
  .keyboard-key {
    @apply px-3 py-4 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md font-semibold uppercase transition-all duration-200 cursor-pointer select-none;
  }

  .keyboard-key.correct {
    @apply bg-green-500 hover:bg-green-600 text-white border-green-500;
  }

  .keyboard-key.present {
    @apply bg-yellow-500 hover:bg-yellow-600 text-white border-yellow-500;
  }

  .keyboard-key.absent {
    @apply bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-700 text-white border-gray-500 dark:border-gray-600;
  }

  .keyboard-key.wide {
    @apply px-6;
  }

  /* Animation classes */
  .tile-flip {
    animation: flip 0.6s ease-in-out;
  }

  .tile-bounce {
    animation: bounceIn 0.6s ease-out;
  }

  .row-shake {
    animation: shake 0.5s ease-in-out;
  }

  /* Glass morphism effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .glass.dark {
    @apply bg-black/10 border-white/10;
  }
}

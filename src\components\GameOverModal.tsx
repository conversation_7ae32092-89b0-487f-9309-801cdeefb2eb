import React from "react";
import { motion } from "framer-motion";
import {
  Trophy,
  Target,
  Clock,
  Share2,
  RotateCcw,
  TrendingUp,
} from "lucide-react";
import { useGameStore } from "@/stores/gameStore";
import { Modal } from "./Modal";
import toast from "react-hot-toast";

export function GameOverModal() {
  const { gameState, currentWord, session, stats, resetGame, toggleStats } =
    useGameStore();

  const isWin = gameState === "won";
  const guessCount = session?.guesses.length || 0;
  const timeElapsed = session
    ? Math.round((new Date().getTime() - session.startTime.getTime()) / 1000)
    : 0;

  const handleShare = () => {
    const emoji = isWin ? "🟩" : "🟥";
    const result = isWin
      ? `${guessCount}/${session?.settings.maxGuesses}`
      : "X/6";
    const difficulty = session?.settings.difficulty || "normal";

    const shareText = `Wordly2 ${result} (${difficulty})\n${emoji.repeat(guessCount)}`;

    if (navigator.share) {
      navigator.share({
        title: "Wordly2 Result",
        text: shareText,
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success("Result copied to clipboard!");
    }
  };

  const handleNewGame = () => {
    resetGame();
  };

  return (
    <Modal
      isOpen={gameState === "won" || gameState === "lost"}
      onClose={() => {}}
      title={isWin ? "🎉 Congratulations!" : "😔 Game Over"}
      maxWidth="max-w-md"
    >
      <div className="text-center space-y-6">
        {/* Result Message */}
        <div>
          {isWin ? (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <Trophy className="w-16 h-16 text-success-500 mx-auto mb-4" />
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                You found the word in {guessCount} guess
                {guessCount !== 1 ? "es" : ""}!
              </p>
            </motion.div>
          ) : (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <Target className="w-16 h-16 text-error-500 mx-auto mb-4" />
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Better luck next time!
              </p>
              <p className="text-gray-500 dark:text-gray-400">
                The word was:{" "}
                <span className="font-bold text-gray-900 dark:text-gray-100">
                  {currentWord}
                </span>
              </p>
            </motion.div>
          )}
        </div>

        {/* Game Stats */}
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            className="bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Target className="w-6 h-6 text-blue-500 mx-auto mb-2" />
            <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {guessCount}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Guesses
            </div>
          </motion.div>

          <motion.div
            className="bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Clock className="w-6 h-6 text-warning-500 mx-auto mb-2" />
            <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {timeElapsed}s
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Time</div>
          </motion.div>
        </div>

        {/* Current Streak */}
        {isWin && stats.currentStreak > 1 && (
          <motion.div
            className="bg-gradient-to-r from-success-500/10 to-success-600/10 border border-success-500/20 rounded-lg p-4"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
          >
            <div className="flex items-center justify-center gap-2 text-success-600">
              <TrendingUp className="w-5 h-5" />
              <span className="font-semibold">
                {stats.currentStreak} game win streak! 🔥
              </span>
            </div>
          </motion.div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <motion.button
            onClick={handleShare}
            className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Share2 size={18} />
            Share
          </motion.button>

          <motion.button
            onClick={handleNewGame}
            className="flex-1 bg-secondary hover:bg-secondary/80 text-secondary-foreground px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <RotateCcw size={18} />
            New Game
          </motion.button>
        </div>

        {/* View Stats Link */}
        <motion.button
          onClick={toggleStats}
          className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors underline"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          View detailed statistics
        </motion.button>
      </div>
    </Modal>
  );
}

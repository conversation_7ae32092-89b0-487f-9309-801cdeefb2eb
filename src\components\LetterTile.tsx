import React from "react";
import { motion } from "framer-motion";
import { clsx } from "clsx";
import type { Letter } from "@/types";

interface LetterTileProps {
  letter: Letter;
  isSubmitted: boolean;
  isAnimating: boolean;
  animationDelay?: number;
}

export function LetterTile({
  letter,
  isSubmitted,
  isAnimating,
  animationDelay = 0,
}: LetterTileProps) {
  const isEmpty = letter.char === "";
  const isFilled = letter.char !== "" && !isSubmitted;

  const tileClasses = clsx("letter-tile", {
    empty: isEmpty,
    filled: isFilled,
    correct: isSubmitted && letter.state === "correct",
    present: isSubmitted && letter.state === "present",
    absent: isSubmitted && letter.state === "absent",
  });

  const variants = {
    initial: {
      scale: 1,
      rotateY: 0,
      backgroundColor: isEmpty ? "transparent" : "#f3f4f6",
    },
    filled: {
      scale: 1.05,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    },
    flip: {
      rotateY: [0, 90, 0],
      backgroundColor: [
        "#f3f4f6",
        "#f3f4f6",
        letter.state === "correct"
          ? "#22c55e"
          : letter.state === "present"
            ? "#f59e0b"
            : "#6b7280",
      ],
      transition: {
        duration: 0.6,
        delay: animationDelay / 1000,
        times: [0, 0.5, 1],
      },
    },
    bounce: {
      scale: [1, 1.1, 1],
      transition: {
        duration: 0.3,
        delay: animationDelay / 1000,
        times: [0, 0.5, 1],
      },
    },
  };

  return (
    <motion.div
      className={tileClasses}
      variants={variants}
      initial="initial"
      animate={
        isAnimating && isSubmitted ? "flip" : isFilled ? "filled" : "initial"
      }
      whileHover={!isSubmitted ? { scale: 1.02 } : {}}
      whileTap={!isSubmitted ? { scale: 0.98 } : {}}
    >
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
        className="select-none"
      >
        {letter.char}
      </motion.span>
    </motion.div>
  );
}
